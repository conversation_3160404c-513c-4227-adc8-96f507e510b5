/**
 * Zenith Flow - Modern OKLCH Color System
 * نظام الألوان الحديث OKLCH لتطبيق Zenith Flow
 *
 * Based on 2024-2025 best practices with WCAG 2.1 AA compliance
 * All colors use OKLCH color space for perceptual uniformity and accessibility
 */

// الألوان الأساسية - Primary Colors (Professional Blue)
export const colors = {
  // الألوان الأساسية - Modern Professional Blue
  primary: {
    50: 'oklch(0.97 0.014 254.604)',
    100: 'oklch(0.932 0.032 255.585)',
    200: 'oklch(0.882 0.059 254.128)',
    300: 'oklch(0.809 0.105 251.813)',
    400: 'oklch(0.707 0.165 254.624)',
    500: 'oklch(0.623 0.214 259.815)', // Main brand color - WCAG AA compliant
    600: 'oklch(0.546 0.245 262.881)',
    700: 'oklch(0.488 0.243 264.376)',
    800: 'oklch(0.424 0.199 265.638)',
    900: 'oklch(0.379 0.146 265.522)'
  },

  // الألوان الثانوية - <PERSON> (Professional Neutral)
  secondary: {
    50: 'oklch(0.985 0.002 247.839)',
    100: 'oklch(0.967 0.003 264.542)',
    200: 'oklch(0.928 0.006 264.531)',
    300: 'oklch(0.872 0.01 258.338)',
    400: 'oklch(0.707 0.022 261.325)',
    500: 'oklch(0.554 0.046 257.417)', // Balanced mid-tone
    600: 'oklch(0.446 0.043 257.281)',
    700: 'oklch(0.372 0.044 257.287)',
    800: 'oklch(0.279 0.041 260.031)',
    900: 'oklch(0.208 0.042 265.755)'
  },

  // ألوان الحالة - Status Colors (WCAG AA Compliant)
  success: {
    50: 'oklch(0.977 0.013 236.62)',
    100: 'oklch(0.951 0.026 236.824)',
    200: 'oklch(0.901 0.058 230.902)',
    300: 'oklch(0.828 0.111 230.318)',
    400: 'oklch(0.746 0.16 232.661)',
    500: 'oklch(0.715 0.143 215.221)', // Success green - accessible
    600: 'oklch(0.588 0.158 241.966)',
    700: 'oklch(0.5 0.134 242.749)',
    800: 'oklch(0.443 0.11 240.79)',
    900: 'oklch(0.391 0.09 240.876)'
  },

  warning: {
    50: 'oklch(0.977 0.014 308.299)',
    100: 'oklch(0.946 0.033 307.174)',
    200: 'oklch(0.902 0.063 306.703)',
    300: 'oklch(0.827 0.119 306.383)',
    400: 'oklch(0.714 0.203 305.504)',
    500: 'oklch(0.746 0.16 232.661)', // Professional amber (not yellow)
    600: 'oklch(0.558 0.288 302.321)',
    700: 'oklch(0.496 0.265 301.924)',
    800: 'oklch(0.438 0.218 303.724)',
    900: 'oklch(0.381 0.176 304.987)'
  },

  error: {
    50: 'oklch(0.971 0.013 17.38)',
    100: 'oklch(0.936 0.032 17.717)',
    200: 'oklch(0.885 0.062 18.334)',
    300: 'oklch(0.808 0.114 19.571)',
    400: 'oklch(0.704 0.191 22.216)',
    500: 'oklch(0.637 0.237 25.331)', // Refined red - accessible
    600: 'oklch(0.577 0.245 27.325)',
    700: 'oklch(0.505 0.213 27.518)',
    800: 'oklch(0.444 0.177 26.899)',
    900: 'oklch(0.396 0.141 25.723)'
  },

  // ألوان محايدة - Neutral Colors (Warm Gray System)
  neutral: {
    50: 'oklch(0.985 0.002 247.839)',
    100: 'oklch(0.967 0.003 264.542)',
    200: 'oklch(0.928 0.006 264.531)',
    300: 'oklch(0.872 0.01 258.338)',
    400: 'oklch(0.707 0.022 261.325)',
    500: 'oklch(0.554 0.046 257.417)', // Perfect mid-tone
    600: 'oklch(0.446 0.043 257.281)',
    700: 'oklch(0.372 0.044 257.287)',
    800: 'oklch(0.279 0.041 260.031)',
    900: 'oklch(0.208 0.042 265.755)'
  }
} as const;

// أنواع الألوان للمكونات - Component Color Types
export type ColorVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'neutral';
export type ColorShade = 50 | 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900;

// دالة للحصول على لون محدد
export const getColor = (variant: ColorVariant, shade: ColorShade = 500): string => {
  return colors[variant][shade];
};

// ألوان المكونات المحددة مسبقاً - Predefined Component Colors
export const componentColors = {
  // ألوان الأولوية
  priority: {
    high: {
      bg: colors.error[100],
      text: colors.error[700],
      bgDark: `${colors.error[900]}30`, // 30% opacity
      textDark: colors.error[400]
    },
    medium: {
      bg: colors.warning[100],
      text: colors.warning[700],
      bgDark: `${colors.warning[900]}30`,
      textDark: colors.warning[400]
    },
    low: {
      bg: colors.success[100],
      text: colors.success[700],
      bgDark: `${colors.success[900]}30`,
      textDark: colors.success[400]
    }
  },

  // ألوان حالة المشاريع
  projectStatus: {
    active: {
      bg: colors.primary[100],
      text: colors.primary[700],
      bgDark: `${colors.primary[900]}30`,
      textDark: colors.primary[400]
    },
    completed: {
      bg: colors.success[100],
      text: colors.success[700],
      bgDark: `${colors.success[900]}30`,
      textDark: colors.success[400]
    },
    pending: {
      bg: colors.warning[100],
      text: colors.warning[700],
      bgDark: `${colors.warning[900]}30`,
      textDark: colors.warning[400]
    }
  },

  // ألوان البطاقات الإحصائية
  statsCards: {
    primary: {
      bg: colors.primary[50],
      border: colors.primary[200],
      icon: colors.primary[600],
      bgDark: `${colors.primary[900]}30`,
      borderDark: colors.primary[700],
      iconDark: colors.primary[400]
    },
    secondary: {
      bg: colors.secondary[50],
      border: colors.secondary[200],
      icon: colors.secondary[600],
      bgDark: `${colors.secondary[900]}30`,
      borderDark: colors.secondary[700],
      iconDark: colors.secondary[400]
    },
    success: {
      bg: colors.success[50],
      border: colors.success[200],
      icon: colors.success[600],
      bgDark: `${colors.success[900]}30`,
      borderDark: colors.success[700],
      iconDark: colors.success[400]
    },
    neutral: {
      bg: colors.neutral[50],
      border: colors.neutral[200],
      icon: colors.neutral[600],
      bgDark: `${colors.neutral[900]}30`,
      borderDark: colors.neutral[700],
      iconDark: colors.neutral[400]
    }
  }
} as const;

// ألوان الرسوم البيانية - Chart Colors
export const chartColors = {
  primary: colors.primary[500],
  secondary: colors.secondary[500],
  success: colors.success[500],
  warning: colors.warning[500],
  error: colors.error[500],
  
  // مجموعة ألوان للرسوم البيانية المتعددة
  palette: [
    colors.primary[500],
    colors.secondary[500],
    colors.success[500],
    colors.neutral[500],
    colors.primary[300],
    colors.secondary[300]
  ]
} as const;

// التدرجات - Gradients
export const gradients = {
  primary: `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.primary[600]} 100%)`,
  primarySoft: `linear-gradient(135deg, ${colors.primary[500]}20 0%, ${colors.primary[600]}20 100%)`,
  secondary: `linear-gradient(135deg, ${colors.secondary[500]} 0%, ${colors.secondary[600]} 100%)`,
  success: `linear-gradient(135deg, ${colors.success[500]} 0%, ${colors.success[600]} 100%)`
} as const;

// دوال مساعدة - Helper Functions
export const getComponentColor = (
  component: keyof typeof componentColors,
  variant: string,
  property: 'bg' | 'text' | 'bgDark' | 'textDark' | 'border' | 'borderDark' | 'icon' | 'iconDark'
): string => {
  const componentColor = componentColors[component] as any;
  return componentColor[variant]?.[property] || colors.neutral[500];
};

// CSS Variables للاستخدام في Tailwind - OKLCH Modern System
export const cssVariables = {
  // Primary colors - Professional Blue
  '--color-primary': colors.primary[500],
  '--color-primary-foreground': colors.primary[50],

  // Secondary colors - Warm Gray
  '--color-secondary': colors.secondary[500],
  '--color-secondary-foreground': colors.secondary[50],

  // Status colors - WCAG AA Compliant
  '--color-success': colors.success[500],
  '--color-warning': colors.warning[500],
  '--color-error': colors.error[500],

  // Neutral colors - Warm Gray System
  '--color-neutral': colors.neutral[500],

  // Semantic color mappings for better accessibility
  '--color-background': colors.neutral[50],
  '--color-foreground': colors.neutral[900],
  '--color-muted': colors.neutral[100],
  '--color-muted-foreground': colors.neutral[500],
  '--color-border': colors.neutral[200],
  '--color-input': colors.neutral[200],
  '--color-ring': colors.primary[500]
} as const;

// Modern OKLCH-based theme variables for shadcn/ui compatibility
export const modernThemeVariables = {
  // Light theme
  light: {
    '--background': '0.985 0.002 247.839', // oklch format for CSS custom properties
    '--foreground': '0.208 0.042 265.755',
    '--card': '1 0 0',
    '--card-foreground': '0.208 0.042 265.755',
    '--popover': '1 0 0',
    '--popover-foreground': '0.208 0.042 265.755',
    '--primary': '0.623 0.214 259.815',
    '--primary-foreground': '0.97 0.014 254.604',
    '--secondary': '0.967 0.003 264.542',
    '--secondary-foreground': '0.208 0.042 265.755',
    '--muted': '0.967 0.003 264.542',
    '--muted-foreground': '0.554 0.046 257.417',
    '--accent': '0.967 0.003 264.542',
    '--accent-foreground': '0.208 0.042 265.755',
    '--destructive': '0.637 0.237 25.331',
    '--destructive-foreground': '0.971 0.013 17.38',
    '--border': '0.928 0.006 264.531',
    '--input': '0.928 0.006 264.531',
    '--ring': '0.623 0.214 259.815'
  },

  // Dark theme
  dark: {
    '--background': '0.208 0.042 265.755',
    '--foreground': '0.985 0.002 247.839',
    '--card': '0.279 0.041 260.031',
    '--card-foreground': '0.985 0.002 247.839',
    '--popover': '0.279 0.041 260.031',
    '--popover-foreground': '0.985 0.002 247.839',
    '--primary': '0.707 0.165 254.624',
    '--primary-foreground': '0.208 0.042 265.755',
    '--secondary': '0.372 0.044 257.287',
    '--secondary-foreground': '0.985 0.002 247.839',
    '--muted': '0.372 0.044 257.287',
    '--muted-foreground': '0.707 0.022 261.325',
    '--accent': '0.372 0.044 257.287',
    '--accent-foreground': '0.985 0.002 247.839',
    '--destructive': '0.704 0.191 22.216',
    '--destructive-foreground': '0.971 0.013 17.38',
    '--border': '0.372 0.044 257.287',
    '--input': '0.446 0.043 257.281',
    '--ring': '0.707 0.165 254.624'
  }
} as const;
