import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        success:
          "border-transparent bg-zenith-success-500 text-white hover:bg-zenith-success-600",
        warning:
          "border-transparent bg-zenith-warning-500 text-white hover:bg-zenith-warning-600",
        outline: "text-foreground border-border",
        outline-primary: "text-primary border-primary/20 bg-primary/5 hover:bg-primary/10",
        outline-success: "text-zenith-success-700 border-zenith-success-200 bg-zenith-success-50 hover:bg-zenith-success-100",
        outline-warning: "text-zenith-warning-700 border-zenith-warning-200 bg-zenith-warning-50 hover:bg-zenith-warning-100",
        outline-destructive: "text-destructive border-destructive/20 bg-destructive/5 hover:bg-destructive/10",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
