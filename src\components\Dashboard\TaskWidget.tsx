
import React from 'react';
import { CheckSquare, Clock, AlertCircle, Plus, Loader2 } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import { useTodayTasks, useTaskMutations } from '@/hooks/useData';
import { Task } from '@/lib/database';

const TaskWidget: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { priority } = useColors();

  // Use real data hooks
  const { data: tasks, loading, refetch } = useTodayTasks();
  const { updateTask, loading: mutationLoading } = useTaskMutations();

  const handleToggleComplete = async (task: Task) => {
    try {
      const newStatus = task.status === 'completed' ? 'todo' : 'completed';
      await updateTask(task.id, { status: newStatus });
      refetch(); // Refresh the data
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  };

  if (loading) {
    return (
      <div className="bg-card border border-border rounded-xl p-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">{t('common.loading')}</span>
        </div>
      </div>
    );
  }
    {
      id: '4',
      title: isRTL ? 'كتابة الوثائق التقنية' : 'Write technical documentation',
      priority: 'low',
      dueDate: '2024-01-18',
      completed: false,
    },
  ];

  const getPriorityColor = (priorityLevel: string) => {
    switch (priorityLevel) {
      case 'high': return priority.high.className;
      case 'medium': return priority.medium.className;
      case 'low': return priority.low.className;
      default: return 'text-muted-foreground bg-muted';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return AlertCircle;
      case 'medium': return Clock;
      case 'low': return CheckSquare;
      default: return CheckSquare;
    }
  };

  return (
    <div className="bg-card border border-border rounded-xl p-6 transition-colors duration-200">
      <div className={cn(
        "flex items-center justify-between mb-6",
        isRTL && "flex-row-reverse"
      )}>
        <h2 className="text-xl font-bold">{t('dashboard.todayTasks')}</h2>
        <button className="flex items-center gap-2 px-3 py-1.5 text-sm bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200">
          <Plus className="w-4 h-4" />
          {t('tasks.newTask')}
        </button>
      </div>

      <div className="space-y-3">
        {!tasks || tasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>{t('dashboard.noTasksToday')}</p>
          </div>
        ) : (
          tasks.slice(0, 5).map((task) => {
            const PriorityIcon = getPriorityIcon(task.priority);
            return (
              <div
                key={task.id}
                className={cn(
                  "flex items-center gap-4 p-4 rounded-lg border transition-all duration-200 group hover:shadow-sm",
                  task.status === 'completed'
                    ? "bg-muted/30 border-muted"
                    : "bg-background border-border hover:border-primary/20",
                  isRTL && "flex-row-reverse"
                )}
              >
                <button
                  onClick={() => handleToggleComplete(task)}
                  disabled={mutationLoading}
                  className={cn(
                    "flex-shrink-0 w-5 h-5 rounded border-2 transition-colors duration-200",
                    task.status === 'completed'
                      ? "bg-primary border-primary text-primary-foreground"
                      : "border-muted-foreground hover:border-primary",
                    mutationLoading && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {task.status === 'completed' && <CheckSquare className="w-3 h-3" />}
                </button>

              <div className={cn("flex-1 min-w-0", isRTL && "text-right")}>
                <p className={cn(
                  "font-medium truncate",
                  task.completed ? "line-through text-muted-foreground" : "text-foreground"
                )}>
                  {task.title}
                </p>
                <div className={cn(
                  "flex items-center gap-2 mt-1 text-xs text-muted-foreground",
                  isRTL && "flex-row-reverse"
                )}>
                  <Clock className="w-3 h-3" />
                  <span>{task.dueDate}</span>
                </div>
              </div>

              <div className={cn(
                "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                getPriorityColor(task.priority)
              )}>
                <PriorityIcon className="w-3 h-3" />
                <span>{t(`tasks.priority.${task.priority}`)}</span>
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-4 pt-4 border-t border-border">
        <div className={cn(
          "flex items-center justify-between text-sm text-muted-foreground",
          isRTL && "flex-row-reverse"
        )}>
          <span>{isRTL ? '3 من 4 مهام مكتملة' : '3 of 4 tasks completed'}</span>
          <span>75%</span>
        </div>
        <div className="w-full bg-muted rounded-full h-2 mt-2">
          <div className="w-3/4 bg-zenith-sage-500 h-full rounded-full transition-all duration-500"></div>
        </div>
      </div>
    </div>
  );
};

export default TaskWidget;
