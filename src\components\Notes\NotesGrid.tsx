
import React, { useState } from 'react';
import { FileText, Plus, Search, Filter, Loader2, AlertCircle, Pin } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useNotes, useNoteMutations } from '@/hooks/useData';
import { Note } from '@/lib/database';

const NotesGrid: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Use real data hooks
  const { data: notes, loading, error, refetch } = useNotes();
  const { togglePin, loading: mutationLoading } = useNoteMutations();

  const handleTogglePin = async (note: Note) => {
    try {
      await togglePin(note.id);
      refetch(); // Refresh the data
    } catch (error) {
      console.error('Failed to toggle pin:', error);
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">{t('common.loading')}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12 text-destructive">
          <AlertCircle className="w-8 h-8 mr-2" />
          <span>{t('common.error')}: {error.message}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(isRTL && "text-right")}>
          <h1 className="text-3xl font-bold">{t('notes.title')}</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">{t('notes.subtitle')}</p>
        </div>
        <button className="flex items-center gap-2 px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200">
          <Plus className="w-5 h-5" />
          {t('notes.newNote')}
        </button>
      </div>

      {/* Search and Filter */}
      <div className={cn(
        "flex items-center gap-4",
        isRTL && "flex-row-reverse"
      )}>
        <div className="flex-1 relative">
          <Search className={cn(
            "absolute top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5",
            isRTL ? "right-3" : "left-3"
          )} />
          <input
            type="text"
            placeholder={isRTL ? 'ابحث في الملاحظات...' : 'Search notes...'}
            className={cn(
              "w-full bg-card border border-border rounded-lg py-3 px-12 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
              isRTL ? "text-right pr-12" : "text-left pl-12"
            )}
          />
        </div>
        <button className="p-3 bg-card hover:bg-accent border border-border rounded-lg transition-colors">
          <Filter className="w-5 h-5" />
        </button>
      </div>

      {/* Notes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {!notes || notes.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <div className="text-muted-foreground mb-4">
              {t('notes.noNotes')}
            </div>
            <button
              onClick={() => setShowCreateForm(true)}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              {t('notes.createFirst')}
            </button>
          </div>
        ) : (
          notes.map((note) => (
            <div
              key={note.id}
              className={cn(
                `bg-gradient-to-br ${note.color} border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200 cursor-pointer group relative`
              )}
            >
              {/* Pin button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleTogglePin(note);
                }}
                disabled={mutationLoading}
                className={cn(
                  "absolute top-3 right-3 p-1 rounded-full transition-colors",
                  note.isPinned
                    ? "text-primary bg-primary/10"
                    : "text-muted-foreground hover:text-primary hover:bg-primary/10",
                  mutationLoading && "opacity-50 cursor-not-allowed"
                )}
              >
                <Pin className="w-4 h-4" />
              </button>

              <div className="mb-4">
                <h3 className={cn(
                  "text-lg font-semibold mb-2 group-hover:text-primary transition-colors pr-8",
                  isRTL && "text-right pl-8 pr-0"
                )}>
                  {note.title}
                </h3>
                <p className={cn(
                  "text-muted-foreground text-sm line-clamp-3",
                  isRTL && "text-right"
                )}>
                  {note.content}
                </p>
              </div>

              <div className={cn(
                "flex items-center justify-between text-xs text-muted-foreground mb-3",
                isRTL && "flex-row-reverse"
              )}>
                <span>{t('notes.lastModified')}: {new Date(note.lastModified).toLocaleDateString()}</span>
                <span>{note.wordCount} {t('notes.wordCount')}</span>
              </div>

              {note.tags && note.tags.length > 0 && (
                <div className={cn(
                  "flex gap-1 flex-wrap",
                  isRTL && "flex-row-reverse"
                )}>
                  {note.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-background/50 text-foreground/70 rounded text-xs font-medium"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          ))
        )}

        {/* Add New Note Card */}
        <div className="bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-6 hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 cursor-pointer group">
          <div className="flex flex-col items-center justify-center h-full text-center py-8">
            <div className="w-12 h-12 bg-zenith-gradient rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
              <FileText className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('notes.newNote')}</h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              {isRTL ? 'ابدأ في كتابة أفكارك' : 'Start writing your thoughts'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotesGrid;
