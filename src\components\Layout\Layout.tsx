/**
 * Zenith Pulse Manager - Main Layout Component
 * مكون التخطيط الرئيسي لتطبيق Zenith Pulse Manager
 * 
 * Main application layout with sidebar, header, and theme support
 */

import React, { useState } from 'react';
import { Menu, X, Search, Bell, User } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { SimpleThemeToggle } from '@/contexts/ThemeContext';
import { cn } from '@/lib/utils';
import Sidebar from './Sidebar';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { t, isRTL } = useLanguage();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-background transition-colors duration-200">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 z-50 w-64 transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
        isRTL ? "right-0" : "left-0",
        sidebarOpen ? "translate-x-0" : (isRTL ? "translate-x-full" : "-translate-x-full")
      )}>
        <Sidebar onClose={() => setSidebarOpen(false)} />
      </div>

      {/* Main content */}
      <div className={cn(
        "flex-1 lg:ml-64",
        isRTL && "lg:ml-0 lg:mr-64"
      )}>
        {/* Header */}
        <header className="bg-card border-b border-border px-4 py-3 lg:px-6">
          <div className="flex items-center justify-between">
            {/* Left side - Mobile menu button */}
            <div className="flex items-center gap-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="p-2 rounded-lg hover:bg-muted transition-colors lg:hidden"
              >
                <Menu className="w-5 h-5" />
              </button>

              {/* Search bar */}
              <div className="hidden md:flex items-center gap-2 bg-muted rounded-lg px-3 py-2 min-w-[300px]">
                <Search className="w-4 h-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder={t('common.search')}
                  className={cn(
                    "bg-transparent border-none outline-none flex-1 text-sm",
                    isRTL && "text-right"
                  )}
                />
              </div>
            </div>

            {/* Right side - Actions */}
            <div className="flex items-center gap-2">
              {/* Mobile search button */}
              <button className="p-2 rounded-lg hover:bg-muted transition-colors md:hidden">
                <Search className="w-5 h-5" />
              </button>

              {/* Theme toggle */}
              <SimpleThemeToggle />

              {/* Notifications */}
              <button className="p-2 rounded-lg hover:bg-muted transition-colors relative">
                <Bell className="w-5 h-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-primary rounded-full"></span>
              </button>

              {/* User menu */}
              <button className="p-2 rounded-lg hover:bg-muted transition-colors">
                <User className="w-5 h-5" />
              </button>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
