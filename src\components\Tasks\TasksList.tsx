
import React, { useState } from 'react';
import { CheckSquare, Plus, Filter, Search, Loader2, AlertCircle } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import { useTasks, useTaskMutations } from '@/hooks/useData';
import { Task } from '@/lib/database';

const TasksList: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { priority } = useColors();
  const [filter, setFilter] = useState('all');
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Use real data hooks
  const { data: tasks, loading, error, refetch } = useTasks();
  const { updateTask, loading: mutationLoading } = useTaskMutations();

  // Filter tasks based on current filter
  const filteredTasks = tasks?.filter(task => {
    if (filter === 'all') return true;
    return task.status === filter;
  }) || [];

  const handleToggleComplete = async (task: Task) => {
    try {
      const newStatus = task.status === 'completed' ? 'todo' : 'completed';
      await updateTask(task.id, { status: newStatus });
      refetch(); // Refresh the data
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">{t('common.loading')}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12 text-destructive">
          <AlertCircle className="w-8 h-8 mr-2" />
          <span>{t('common.error')}: {error.message}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(isRTL && "text-right")}>
          <h1 className="text-3xl font-bold">{t('tasks.title')}</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">{t('tasks.subtitle')}</p>
        </div>
        <button className="flex items-center gap-2 px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200">
          <Plus className="w-5 h-5" />
          {t('tasks.newTask')}
        </button>
      </div>

      {/* Filters */}
      <div className={cn(
        "flex items-center gap-4",
        isRTL && "flex-row-reverse"
      )}>
        <div className="flex gap-2">
          {['all', 'todo', 'inProgress', 'completed'].map((status) => (
            <button
              key={status}
              onClick={() => setFilter(status)}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                filter === status
                  ? "bg-zenith-gradient text-white shadow-zenith"
                  : "bg-card hover:bg-accent text-foreground"
              )}
            >
              {t(`tasks.status.${status}` || status)}
            </button>
          ))}
        </div>
        
        <div className="flex gap-2 ml-auto">
          <button className="p-2 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg transition-colors">
            <Filter className="w-5 h-5" />
          </button>
          <button className="p-2 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg transition-colors">
            <Search className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Tasks Grid */}
      <div className="grid gap-4">
        {filteredTasks.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-muted-foreground mb-4">
              {filter === 'all' ? t('tasks.noTasks') : t('tasks.noTasksInFilter')}
            </div>
            <button
              onClick={() => setShowCreateForm(true)}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              {t('tasks.createFirst')}
            </button>
          </div>
        ) : (
          filteredTasks.map((task) => (
            <div
              key={task.id}
              className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200"
            >
              <div className={cn(
                "flex items-start gap-4",
                isRTL && "flex-row-reverse"
              )}>
                <button
                  onClick={() => handleToggleComplete(task)}
                  disabled={mutationLoading}
                  className={cn(
                    "flex-shrink-0 w-6 h-6 rounded border-2 transition-colors",
                    task.status === 'completed'
                      ? "bg-primary border-primary text-primary-foreground"
                      : "border-muted-foreground hover:border-primary",
                    mutationLoading && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {task.status === 'completed' && <CheckSquare className="w-4 h-4" />}
                </button>

                <div className={cn("flex-1", isRTL && "text-right")}>
                  <h3 className={cn(
                    "text-lg font-semibold mb-2",
                    task.status === 'completed' && "line-through text-muted-foreground"
                  )}>
                    {task.title}
                  </h3>
                  {task.description && (
                    <p className="text-muted-foreground mb-4">{task.description}</p>
                  )}
                
                  <div className={cn(
                    "flex items-center gap-4 flex-wrap",
                    isRTL && "flex-row-reverse"
                  )}>
                    <span className={cn(
                      "text-xs px-2 py-1 rounded-full font-medium",
                      task.priority === 'high'
                        ? priority.high.className
                        : task.priority === 'medium'
                        ? priority.medium.className
                        : priority.low.className
                    )}>
                      {t(`tasks.priority.${task.priority}`)}
                    </span>

                    <span className={cn(
                      "text-xs px-2 py-1 rounded-full font-medium",
                      task.status === 'completed'
                        ? "bg-success/10 text-success"
                        : task.status === 'inProgress'
                        ? "bg-warning/10 text-warning"
                        : "bg-muted text-muted-foreground"
                    )}>
                      {t(`tasks.status.${task.status}`)}
                    </span>

                    {task.dueDate && (
                      <span className="text-sm text-muted-foreground">
                        {t('tasks.dueDate')}: {new Date(task.dueDate).toLocaleDateString()}
                      </span>
                    )}

                    {task.tags && task.tags.length > 0 && (
                      <div className="flex gap-1">
                        {task.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="text-xs px-2 py-1 bg-muted text-muted-foreground rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default TasksList;
