<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>آلة حاسبة بسيطة</title>
  <style>
    /* ===== تنسيق الآلة الحاسبة ===== */
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background: #f0f0f5;
      margin: 0;
      font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    }
    .calculator {
      background: #ffffff;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      width: 300px;
    }
    .display {
      width: 100%;
      height: 50px;
      font-size: 1.5rem;
      text-align: right;
      padding: 0 10px;
      border: 2px solid #ddd;
      border-radius: 5px;
      margin-bottom: 10px;
    }
    .keys {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 10px;
    }
    .keys button {
      font-size: 1.2rem;
      padding: 15px;
      border: none;
      border-radius: 5px;
      background: #e6e6e6;
      cursor: pointer;
      transition: background 0.2s;
    }
    .keys button:hover {
      background: #d4d4d4;
    }
    .keys .operator {
      background: #ff9500;
      color: #fff;
    }
    .keys .operator:hover {
      background: #e08500;
    }
    .keys .equal {
      background: #34c759;
      color: #fff;
      grid-column: span 2;
    }
    .keys .equal:hover {
      background: #28a745;
    }
    .keys .clear {
      background: #ff3b30;
      color: #fff;
    }
    .keys .clear:hover {
      background: #e12d23;
    }
  </style>
</head>
<body>
  <div class="calculator">
    <input type="text" class="display" id="display" readonly />
    <div class="keys">
      <!-- الصف الأول -->
      <button class="clear" id="clear">C</button>
      <button class="operator" data-op="/">÷</button>
      <button class="operator" data-op="*">×</button>
      <button class="operator" data-op="-">−</button>
      <!-- الأرقام -->
      <button>7</button><button>8</button><button>9</button>
      <button class="operator" data-op="+">+</button>
      <button>4</button><button>5</button><button>6</button>
      <button class="equal" id="equal">=</button>
      <button>1</button><button>2</button><button>3</button>
      <button>0</button><button>.</button>
    </div>
  </div>

  <script>
    // ===== منطق الآلة الحاسبة =====
    const display = document.getElementById('display');
    const keys = document.querySelector('.keys');

    keys.addEventListener('click', e => {
      const target = e.target;
      if (!target.matches('button')) return;

      const key = target.textContent;
      const action = target.classList.contains('operator') ? 'operator'
                    : target.id === 'equal'           ? 'equal'
                    : target.id === 'clear'           ? 'clear'
                    : 'number';

      if (action === 'number' || key === '.') {
        // أضف الرقم أو النقطة
        display.value += key;
      }

      if (action === 'operator') {
        display.value += ` ${target.dataset.op} `;
      }

      if (action === 'clear') {
        display.value = '';
      }

      if (action === 'equal') {
        try {
          // استخدم eval بحذر؛ في مشاريع حقيقية استعمل مكتبة تحلّل التعبير
          const result = eval(display.value);
          display.value = result;
        } catch {
          display.value = 'خطأ';
        }
      }
    });
  </script>
</body>
</html>