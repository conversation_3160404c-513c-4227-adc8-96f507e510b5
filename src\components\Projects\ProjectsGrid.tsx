
import React, { useState } from 'react';
import { Calendar, Users, TrendingUp, Plus, Loader2, AlertCircle } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import { useProjects, useProjectMutations } from '@/hooks/useData';
import { Project } from '@/lib/database';

const ProjectsGrid: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { projectStatus } = useColors();
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Use real data hooks
  const { data: projects, loading, error, refetch } = useProjects();
  const { updateProject, loading: mutationLoading } = useProjectMutations();

  if (loading) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">{t('common.loading')}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12 text-destructive">
          <AlertCircle className="w-8 h-8 mr-2" />
          <span>{t('common.error')}: {error.message}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(isRTL && "text-right")}>
          <h1 className="text-3xl font-bold">{t('projects.title')}</h1>
          <p className="text-muted-foreground mt-2">{t('projects.subtitle')}</p>
        </div>
        <button className="flex items-center gap-2 px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200">
          <Plus className="w-5 h-5" />
          {t('projects.newProject')}
        </button>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {!projects || projects.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <div className="text-muted-foreground mb-4">
              {t('projects.noProjects')}
            </div>
            <button
              onClick={() => setShowCreateForm(true)}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              {t('projects.createFirst')}
            </button>
          </div>
        ) : (
          projects.map((project) => (
            <div
              key={project.id}
              className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200 group"
            >
              {/* Project Header */}
              <div className="mb-4">
                <div className={cn(
                  "flex items-start justify-between mb-3",
                  isRTL && "flex-row-reverse"
                )}>
                  <div className={cn(
                    `w-3 h-3 rounded-full bg-gradient-to-r ${project.color}`
                  )}></div>
                  <span className={cn(
                    "text-xs px-2 py-1 rounded-full font-medium",
                    project.status === 'active'
                      ? projectStatus.active.className
                      : project.status === 'completed'
                      ? projectStatus.completed.className
                      : projectStatus.pending.className
                  )}>
                    {t(`projects.status.${project.status}`)}
                  </span>
                </div>

                <h3 className={cn(
                  "text-lg font-semibold mb-2 group-hover:text-primary transition-colors",
                  isRTL && "text-right"
                )}>
                  {project.title}
                </h3>

                <p className={cn(
                  "text-muted-foreground text-sm",
                  isRTL && "text-right"
                )}>
                  {project.description}
                </p>
              </div>

            {/* Progress */}
            <div className="mb-4">
              <div className={cn(
                "flex items-center justify-between text-sm mb-2",
                isRTL && "flex-row-reverse"
              )}>
                <span className="text-muted-foreground">{t('projects.progress')}</span>
                <span className="font-medium">{project.progress}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className={cn(
                    `h-full rounded-full bg-gradient-to-r ${project.color} transition-all duration-500`
                  )}
                  style={{ width: `${project.progress}%` }}
                ></div>
              </div>
            </div>

              {/* Stats */}
              <div className={cn(
                "flex items-center justify-between text-sm text-muted-foreground",
                isRTL && "flex-row-reverse"
              )}>
                <div className={cn(
                  "flex items-center gap-1",
                  isRTL && "flex-row-reverse"
                )}>
                  <Users className="w-4 h-4" />
                  <span>{project.team?.length || 0} {t('projects.members')}</span>
                </div>

                <div className={cn(
                  "flex items-center gap-1",
                  isRTL && "flex-row-reverse"
                )}>
                  <TrendingUp className="w-4 h-4" />
                  <span>{project.tasks?.length || 0} {t('projects.tasks')}</span>
                </div>

                {project.deadline && (
                  <div className={cn(
                    "flex items-center gap-1",
                    isRTL && "flex-row-reverse"
                  )}>
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(project.deadline).toLocaleDateString()}</span>
                  </div>
                )}
              </div>
            </div>
          ))
        )}

        {/* Add New Project Card */}
        <div className="bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-6 hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 cursor-pointer group">
          <div className="flex flex-col items-center justify-center h-full text-center py-8">
            <div className="w-12 h-12 bg-zenith-gradient rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
              <Plus className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('projects.createNew')}</h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">{t('projects.startOrganizing')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectsGrid;
